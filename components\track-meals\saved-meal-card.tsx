import { CustomText } from '@/components/ui/text';
import { Image, Pressable, View } from 'react-native';
import { SavedMealItem } from '@/constants/saved-meals';
import { Ionicons } from '@expo/vector-icons';

interface SavedMealCardProps {
  meal: SavedMealItem;
  onAdd: (meal: SavedMealItem) => void;
}

const getTimeAgo = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days === 1) return 'yesterday';
  return `${days}d ago`;
};

export const SavedMealCard = ({ meal, onAdd }: SavedMealCardProps) => {
  return (
    <View className="mb-3 flex w-full flex-row items-center justify-between overflow-hidden rounded-2xl bg-white p-4 shadow-sm">
      <View className="flex flex-row items-center gap-3 flex-1">
        <Image source={{ uri: meal.uri }} className="size-20 rounded-xl" />
        <View className="flex flex-1 flex-col items-start justify-center gap-1">
          <CustomText variant={'h4'} className="text-primary">
            {meal.mealName}
          </CustomText>
          <CustomText className="text-xs text-muted-foreground">
            Saved {getTimeAgo(meal.savedAt)}
          </CustomText>
        </View>
      </View>
      <Pressable
        onPress={() => onAdd(meal)}
        className="ml-2 flex h-12 w-12 items-center justify-center rounded-full bg-accent">
        <Ionicons name="add" size={24} color="white" />
      </Pressable>
    </View>
  );
};

