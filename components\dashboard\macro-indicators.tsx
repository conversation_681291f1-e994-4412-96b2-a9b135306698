import { View } from 'react-native';
import ProgressBar from '../ui/progress-bar';
import { CustomText } from '../ui/text';

const MacroIndicators = ({
  data,
}: {
  data: { daily_carbs_g: string; daily_fat_g: string; daily_protein_g: string };
}) => {
  return (
    <View className="gap-y-3">
      <CustomText variant="h3">Macros</CustomText>

      <View>
        <View className="flex flex-row items-center justify-between">
          <CustomText>Protein</CustomText>
          <CustomText>{`100/${data.daily_protein_g}g`}</CustomText>
        </View>
        <ProgressBar progress={100 / Number(data.daily_protein_g)} />
      </View>

      <View>
        <View className="flex flex-row items-center justify-between">
          <CustomText>Carbs</CustomText>
          <CustomText>{`80/${data.daily_carbs_g}g`}</CustomText>
        </View>
        <ProgressBar progress={80 / Number(data.daily_carbs_g)} />
      </View>

      <View>
        <View className="flex flex-row items-center justify-between">
          <CustomText>Fats</CustomText>
          <CustomText>{`10/${data.daily_fat_g}g`}</CustomText>
        </View>
        <ProgressBar progress={10 / Number(data.daily_fat_g)} />
      </View>
    </View>
  );
};

export default MacroIndicators;
