import apiClient from '@/lib/axios';
import { useMutation, useInfiniteQuery, type UseMutationOptions } from '@tanstack/react-query';
import { ScanRequest, ScanResponse, ScanHistoryResponse } from './types';

export const useScan = (
  options?: Omit<UseMutationOptions<ScanResponse, Error, ScanRequest>, 'mutationFn'>
) =>
  useMutation<ScanResponse, Error, ScanRequest>({
    mutationFn: async (data: ScanRequest) => {
      const formData = new FormData();
      formData.append('image_base64', data.image_base64);

      const response = await apiClient.post('/api/v1/scan/food/analyze/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      console.log(response.data, 'response data');
      return response.data;
    },
    ...options,
  });

export const useScanHistory = (options?: any) =>
  useInfiniteQuery({
    queryKey: ['scan-history'],
    queryFn: async ({ pageParam }: { pageParam?: string }) => {
      const url = pageParam || '/api/v1/scan/history/';
      const response = await apiClient.get(url);
      return response.data as ScanHistoryResponse;
    },
    getNextPageParam: (lastPage: ScanHistoryResponse) => {
      // Extract the next page URL from the response
      if (lastPage.next) {
        // Extract just the path and query params from the full URL
        const url = new URL(lastPage.next);
        return url.pathname + url.search;
      }
      return undefined;
    },
    initialPageParam: undefined,
    ...options,
  });
