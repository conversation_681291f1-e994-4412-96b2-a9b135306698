import { View, Text, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CustomText } from '../ui/text';
import { useScanHistory } from '@/services/scan/scan';
import { getMealType } from '@/lib/scan-history-utils';
import { useMemo } from 'react';

interface RecentlyEatenProps {
  name: string;
  kcal: string | number;
  mealType: string;
}

const getMealIcon = (mealType: string) => {
  switch (mealType) {
    case 'Breakfast':
      return 'sunny';
    case 'Lunch':
      return 'restaurant';
    case 'Dinner':
      return 'moon';
    case 'Snack':
    default:
      return 'fast-food';
  }
};

const RecentlyEatenItem = ({ name, kcal, mealType }: RecentlyEatenProps) => (
  <View className="mx-2 my-2 flex-row items-center rounded-2xl bg-white p-3">
    {/* Meal Type Icon */}
    <View className="mr-3 size-12 overflow-hidden rounded-full bg-slate-200">
      <View className="size-full items-center justify-center">
        <Ionicons name={getMealIcon(mealType)} size={24} color="#25898B" />
      </View>
    </View>
    {/* Info column */}
    <View className="flex-1">
      <Text className="font-poppins text-base font-medium text-gray-900">{name}</Text>
      <Text className="mt-1 font-lexend text-xs text-teal-600">{mealType}</Text>
    </View>
    {/* Kcal */}
    <Text className="font-lexend text-base text-gray-900">{kcal} cal</Text>
  </View>
);

const RecentlyEaten = () => {
  const { data: scanHistoryData, isLoading, isError } = useScanHistory();

  // Get first 3 items from the API results
  const recentMeals = useMemo(() => {
    if (!scanHistoryData?.pages?.[0]?.results) return [];
    return scanHistoryData.pages[0].results.slice(0, 3);
  }, [scanHistoryData]);

  if (isLoading) {
    return (
      <View className="mt-5">
        <CustomText variant="h3">Recently Eaten</CustomText>
        <View className="items-center py-4">
          <ActivityIndicator size="small" color="#006d77" />
        </View>
      </View>
    );
  }

  if (isError || recentMeals.length === 0) {
    return (
      <View className="mt-5">
        <CustomText variant="h3">Recently Eaten</CustomText>
        <View className="mx-2 my-2 rounded-2xl bg-white p-4">
          <Text className="text-center font-lexend text-gray-500">No recent meals found</Text>
        </View>
      </View>
    );
  }

  return (
    <View className="mt-5">
      <CustomText variant="h3">Recently Eaten</CustomText>
      {recentMeals.map((item: any, index: number) => {
        const timestamp = new Date(item.created_at).getTime();
        return (
          <RecentlyEatenItem
            key={index}
            name={item.nutrition_label.food_name}
            kcal={item.nutrition_label.calories}
            mealType={getMealType(timestamp)}
          />
        );
      })}
    </View>
  );
};

export default RecentlyEaten;
