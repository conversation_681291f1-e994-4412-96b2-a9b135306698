import { Macro } from '@/components/scan-and-check/types';
import CustomButton from '@/components/ui/button';
import ProgressBar from '@/components/ui/progress-bar';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { Option, Select } from '@/components/ui/select';
import { CustomText } from '@/components/ui/text';
import { quantityMetrics, quantityValues } from '@/constants/constant-scan-results';
import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import { router, useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import { Image, ScrollView, View } from 'react-native';
import { showToast } from 'react-native-nitro-toast';

type quantityMetric = 'gm' | 'mg';

const AddToLog = () => {
  const [quantity, setQuantity] = useState(500);
  const [metric, setMetric] = useState<quantityMetric>('gm');

  const { data, imageURI } = useLocalSearchParams<{
    data: string;
    imageURI: string;
  }>();

  const parsedData = JSON.parse(data || '{}');

  // Handle both scan results and saved meal data formats
  const macros: Macro[] = parsedData.macros || [];
  const food_name = parsedData.food_name || parsedData.mealName || 'Unknown Food';
  const calories = parsedData.calories || '0 kcal';

  return (
    <SafeAreaContainer className="ios:-mt-10 flex-1 pt-0">
      <ScrollView>
        <View className="flex flex-col items-center justify-between rounded-xl bg-card px-6 pt-6">
          <View className="mb-10 flex w-full flex-row items-center justify-start gap-6">
            <Image
              source={{
                uri: imageURI,
              }}
              className="h-20 w-20 rounded-xl"
              resizeMode="cover"
            />
            <View className="flex-1">
              <CustomText variant={'h3'} className="flex-wrap font-poppins text-primary">
                {food_name}
              </CustomText>
              <CustomText variant={'info'} className="font-poppins text-primary">
                {calories} calories per 100g
              </CustomText>
            </View>
          </View>
          <View className="flex w-full flex-row items-center justify-between">
            <View className="flex w-full flex-1 flex-col gap-3">
              <CustomText variant={'info'} className="ml-5 self-start text-lg">
                Quantity
              </CustomText>
              <Select
                selectedValue={quantity}
                onValueChange={(itemValue) => setQuantity(itemValue as number)}>
                {quantityValues.map((item) => (
                  <Option {...item} key={item.value} />
                ))}
              </Select>
            </View>
            <View className="flex w-full flex-1 flex-col gap-3">
              <CustomText variant={'info'} className="ml-5 self-start text-lg">
                Metric
              </CustomText>
              <Select
                selectedValue={metric}
                onValueChange={(itemValue) => setMetric(itemValue as quantityMetric)}>
                {quantityMetrics.map((item) => (
                  <Option {...item} key={item.value} />
                ))}
              </Select>
            </View>
          </View>
        </View>

        <View className="mt-3 rounded-xl bg-card p-6">
          <CustomText variant="h3" className="mb-5">
            Macro Breakdown
          </CustomText>
          <View className="gap-y-3">
            {macros.map((macro) => (
              <View key={macro.id} className="w-full flex-1">
                <View className="flex flex-row items-center justify-between">
                  <CustomText>{macro.name}</CustomText>
                  <CustomText>{macro.value} / 100g</CustomText>
                </View>
                <ProgressBar progress={Number(macro.value) / 100} />
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
      <CustomButton
        onPress={() => {
          showToast('Meal Logged Successfully!', {
            ...COMMON_TOAST_PROPS,
            type: 'success',
          });
          router.replace('/scan-and-check');
        }}
        buttonVariant="primary"
        className="mt-auto rounded-full">
        <CustomText variant={'buttonText'}>Log Food Entry</CustomText>
      </CustomButton>
    </SafeAreaContainer>
  );
};

export default AddToLog;
