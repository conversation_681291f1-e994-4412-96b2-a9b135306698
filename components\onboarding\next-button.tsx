import { PressableProps } from 'react-native';
import CustomButton from '../ui/button';
import { cn } from '@/lib/utils';
import { CustomText } from '../ui/text';

const NextButton = ({
  className,
  textClassName,
  buttonTitle = 'Next',
  ...props
}: PressableProps & {
  className?: string;
  textClassName?: string;
  buttonTitle?: string;
}) => {
  return (
    <CustomButton
      className={cn('mb-5 mt-auto rounded-full bg-primary', className)}
      onPress={props.onPress}>
      <CustomText className={cn('font-alansans text-xl text-primary-foreground', textClassName)}>
        {buttonTitle}
      </CustomText>
    </CustomButton>
  );
};

export default NextButton;
