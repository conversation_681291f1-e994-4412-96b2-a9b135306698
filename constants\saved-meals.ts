import { MealCardProps } from '@/components/track-meals/types';

export interface SavedMealItem extends MealCardProps {
  savedAt: number;
}

export const savedMeals: SavedMealItem[] = [
  {
    uri: 'https://images.unsplash.com/photo-1555939594-58d7cb561404?w=400&h=400&fit=crop',
    mealName: 'Croissant',
    calories: '280 kcal',
    macros: [
      {
        id: 1,
        key: 'carbs_g',
        name: 'Car<PERSON>',
        value: 30,
      },
      {
        id: 2,
        key: 'fat_g',
        name: 'Fats',
        value: 15,
      },
    ],
    timestamp: new Date().getTime(),
    savedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).getTime(), // 2 hours ago
  },
  {
    uri: 'https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400&h=400&fit=crop',
    mealName: 'Iced Coffee',
    calories: '120 kcal',
    macros: [
      {
        id: 1,
        key: 'carbs_g',
        name: 'Carbs',
        value: 12,
      },
    ],
    timestamp: new Date().getTime(),
    savedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).getTime(), // yesterday
  },
];

