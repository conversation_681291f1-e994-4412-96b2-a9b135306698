{"expo": {"name": "Eatable?", "slug": "eatable?", "version": "1.0.0", "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "experiments": {"tsconfigPaths": true, "reactCompiler": true}, "plugins": ["expo-router", "expo-font", "@react-native-google-signin/google-signin", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": false}], "expo-secure-store"], "orientation": "default", "icon": "./assets/splash.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.beyondbinary.eatable"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.beyondbinary.eatable", "permissions": ["android.permission.CAMERA"]}, "scheme": "eatable"}}