import { EatableInfoBox } from '@/components/scan-and-check/eatable-info';
import { FoodCard } from '@/components/scan-and-check/food-card';
import CustomButton from '@/components/ui/button';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { Ionicons } from '@expo/vector-icons';
import { Redirect, useLocalSearchParams, useRouter } from 'expo-router';
import { ScrollView, View } from 'react-native';
import { eatableResults } from '@/constants/constant-scan-results';
import { showToast } from 'react-native-nitro-toast';
import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import { Macros, ScanResponse } from '@/services/scan/types';

const macroName = (key: string) => {
  // Remove '_g' suffix
  let name = key.replace(/_g$/, '');
  // Replace underscores with spaces
  name = name.replace(/_/g, ' ');
  // Capitalize each word
  name = name.replace(/\b\w/g, (char) => char.toUpperCase());
  // Replace "Fat" with "Fats"
  name = name.replace(/\bFat\b/g, 'Fats');
  return name;
};

export default function FoodScanResultScreen() {
  const router = useRouter();

  const { data: apiResponse, imageURI } = useLocalSearchParams<{
    data: string;
    imageURI: string;
  }>();

  const parsedData: ScanResponse = JSON.parse(apiResponse || '{}');

  if (!parsedData.food_name || !parsedData.calories) {
    showToast('Please scan again!', {
      type: 'warning',
      title: 'Invalid Food Item',
      ...COMMON_TOAST_PROPS,
    });
    return <Redirect href={'/scan-and-check'} />;
  }

  const macrosArray = Object.entries(parsedData.macros)
    .filter(([, value]) => value && value > 0)
    .map(([key, value], index) => ({
      id: index,
      key: key as keyof Macros,
      name: macroName(key),
      value: value,
    }));

  const handleYes = () => {
    router.push({
      pathname: '/add-to-log',
      params: {
        data: JSON.stringify({ ...parsedData, macros: macrosArray }),
        imageURI,
      },
    });
  };

  const handleNo = () => {
    showToast('Meal is not logged.', {
      ...COMMON_TOAST_PROPS,
      title: 'Skipped',
      type: 'info',
    });
    router.back();
  };

  const handleAddLater = () => {
    showToast('Meal is saved. You can add it to your log later.', {
      ...COMMON_TOAST_PROPS,
      title: 'Saved for later',
      type: 'info',
    });
    router.back();
  };

  return (
    <SafeAreaContainer className="ios:-mt-10 flex-1 pt-0">
      <ScrollView>
        <FoodCard
          image={imageURI}
          foodName={parsedData.food_name.toString()}
          calories={parsedData.calories?.toString()}
          macros={macrosArray}
        />

        <EatableInfoBox
          message={parsedData.advice}
          title={parsedData.is_eatable === 'False' ? 'No' : 'Yes'}
          type={parsedData.is_eatable === 'False' ? 'warning' : 'success'}
        />

        <View className="mt-6 w-full rounded-2xl bg-card p-4">
          <CustomText variant="large" className="mb-4 text-center">
            Are you eating this?
          </CustomText>
          <View className="mb-3 flex w-full flex-row items-center justify-center gap-3">
            <CustomButton
              className="w-full flex-1 flex-row gap-2 rounded-full"
              onPress={handleYes}
              buttonVariant="primary">
              <Ionicons name="checkmark-outline" color={'white'} size={24} />
              <CustomText
                variant="buttonText"
                className="font-alansans-bold text-primary-foreground">
                Yes
              </CustomText>
            </CustomButton>

            <CustomButton
              onPress={handleNo}
              buttonVariant="accent"
              className="flex-1 flex-row gap-2 rounded-full border border-accent bg-transparent">
              <Ionicons name="close-outline" color={'#e29578'} size={24} />
              <CustomText variant="buttonText" className="font-alansans-bold text-accent">
                No
              </CustomText>
            </CustomButton>
          </View>

          <CustomButton onPress={handleAddLater} className="rounded-full bg-transparent">
            <CustomText
              variant="buttonText"
              className="font-alansans-bold text-secondary-foreground underline underline-offset-4">
              Add Later
            </CustomText>
          </CustomButton>
        </View>
      </ScrollView>
    </SafeAreaContainer>
  );
}
