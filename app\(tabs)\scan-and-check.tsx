import { CameraCapturedPicture, CameraView, useCameraPermissions } from 'expo-camera';
import { useFocusEffect, useRouter } from 'expo-router';
import { useCallback, useRef, useState } from 'react';
import { ActivityIndicator, Image, Linking, View } from 'react-native';
import ImageCapturedPopup from '@/components/scan-and-check/scan-retake-dialog';
import CustomButton from '@/components/ui/button';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { FOOD_FACTS } from '@/constants/constant-scan-results';
import { COMMON_TOAST_PROPS } from '@/constants/toast-common';
import { useScan } from '@/services/scan/scan';
import { showToast } from 'react-native-nitro-toast';

import { FlipType, SaveFormat, useImageManipulator } from 'expo-image-manipulator';

const EatableScan = () => {
  const [permission, requestPermission] = useCameraPermissions();
  const [continueModalVisible, setContinueModalVisible] = useState(false);
  const [photo, setPhoto] = useState<CameraCapturedPicture | undefined>(undefined);
  const [cameraActive, setCameraActive] = useState(true);
  const [currentFact, setCurrentFact] = useState(FOOD_FACTS[0]);

  const router = useRouter();
  
  const staticImage = require('@/assets/healthy-cat.png');
  const imageUri = Image.resolveAssetSource(staticImage).uri;

  const context = useImageManipulator(imageUri);

  // Set a random fact when component mounts
  useFocusEffect(
    useCallback(() => {
      const randomIndex = Math.floor(Math.random() * FOOD_FACTS.length);
      setCurrentFact(FOOD_FACTS[randomIndex]);
    }, [])
  );

  useFocusEffect(
    useCallback(() => {
      if (!permission?.granted) {
        requestPermission();
      }
      setCameraActive(true);
      return () => {
        setCameraActive(false);
      };
    }, [permission?.granted, requestPermission])
  );

  const ref = useRef<CameraView>(null);

  const { mutate, isPending } = useScan({
    onSuccess(data) {
      router.push({
        pathname: '/scan-results',
        params: {
          data: JSON.stringify(data),
          imageURI: photo?.uri,
        },
      });
    },
    onError(error) {
      showToast(error.message, {
        ...COMMON_TOAST_PROPS,
        type: 'error',
        title: error.name,
        duration: 14000,
      });
    },
  });

  const takePicture = async () => {
    try {
      const photo = await ref.current?.takePictureAsync({
        base64: true,
        shutterSound: false,
      });
      if (photo?.uri) {
        setPhoto(photo);
        setContinueModalVisible(true);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      showToast('Failed to take picture', {
        ...COMMON_TOAST_PROPS,
        type: 'error',
        title: 'Camera Error',
      });
    }
  };

  const handleProceed = async () => {
    setContinueModalVisible(false);
    // Set a new random fact when starting to scan
    const randomIndex = Math.floor(Math.random() * FOOD_FACTS.length);
    setCurrentFact(FOOD_FACTS[randomIndex]);

    if (photo && photo.uri) {
      // const compressedImage = await ImageCompressor.compress(photo.uri, {
      //   compressionMethod: 'auto',
      //   returnableOutputType: 'base64',
      // });
      const compressedImage = await mani
      console.log(compressedImage, 'compressedimage');
      mutate({ image_base64: compressedImage || '' });
    }
  };

  if (!permission) {
    return (
      <SafeAreaContainer className="flex-1 items-center justify-center bg-white">
        <CustomText variant="default">Checking camera permissions...</CustomText>
      </SafeAreaContainer>
    );
  }

  if (!permission.granted && !permission.canAskAgain) {
    return (
      <SafeAreaContainer className="h-auto w-full flex-1 items-center justify-center">
        <View className="flex w-full items-center justify-center gap-3 rounded-xl bg-card p-3">
          <CustomText className="text-center text-primary" variant="h4">
            This app needs permission to your camera.
          </CustomText>
          <CustomButton>
            <CustomText
              onPress={() => {
                if (permission.canAskAgain) {
                  requestPermission();
                } else {
                  Linking.openSettings();
                }
              }}
              variant={'buttonText'}>
              Give Camera Permission
            </CustomText>
          </CustomButton>
        </View>
      </SafeAreaContainer>
    );
  }

  return (
    <View className="flex-1">
      <View className="flex-1">
        {isPending ? (
          <View className="m-auto flex w-80 flex-col items-center justify-center rounded-xl">
            <ActivityIndicator size="large" />
            <View className="flex-row items-center justify-center">
              {/* <FontAwesome6
                name={currentFact.icon as any}
                size={22}
                color="#006D77"
                className=""
              /> */}
              <CustomText className="mt-5 text-center font-lexend text-base leading-6 text-primary">
                {currentFact.text}
              </CustomText>
            </View>
          </View>
        ) : (
          <>
            <CameraView ref={ref} style={{ flex: 1 }} active={cameraActive} />
            <CustomButton
              className="bg-tranparent absolute bottom-10 left-[40vw] size-20 rounded-full border-[5px] border-white"
              onPress={takePicture}
            />
          </>
        )}
        {photo && photo.uri && continueModalVisible && (
          <View className="absolute bottom-8 left-3 flex-1 items-center justify-center bg-white">
            <Image
              className="border-2 border-white"
              source={{ uri: photo.uri }}
              style={{ width: 100, aspectRatio: 1 }}
            />
          </View>
        )}

        <ImageCapturedPopup
          visible={continueModalVisible}
          onProceed={handleProceed}
          onRetake={() => {
            setPhoto(undefined);
            setContinueModalVisible(false);
          }}
        />
      </View>
    </View>
  );
};

export default EatableScan;
