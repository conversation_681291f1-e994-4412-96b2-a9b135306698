import { View } from 'react-native';
import { Macro } from './types';
import { MACRO_ICONS } from '@/constants/constant-scan-results';
import { CustomText } from '../ui/text';
import { cn } from '@/lib/utils';

interface MacroCardProps {
  macro: Macro;
  className?: string;
  valueClassName?: string;
  macroNameClassName?: string;
}

export const MacroCard = ({
  macro,
  className,
  macroNameClassName,
  valueClassName,
}: MacroCardProps) => {
  return (
    <View
      className={cn('flex min-w-16 flex-col items-center justify-center px-1.5 py-1 bg-gray-100 rounded-lg', className)}>
      {MACRO_ICONS[macro.key]}
      <CustomText className={cn('mt-0.5 text-xs font-semibold', valueClassName)}>
        {macro.value}g
      </CustomText>
      <CustomText className={cn('text-xs', macroNameClassName)}>{macro.name}</CustomText>
    </View>
  );
};
