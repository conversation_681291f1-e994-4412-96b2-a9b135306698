import { cn } from '@/lib/utils';
import { Pressable, PressableProps } from 'react-native';

const buttonVariants = {
  primary: 'bg-primary text-primary-foreground',
  secondary: 'bg-secondary text-secondary-foreground',
  accent: 'bg-accent text-accent-foreground',
  muted: 'bg-muted text-muted-foreground',
  destructive: 'bg-destructive text-destructive-foreground',
};

const CustomButton = ({
  buttonVariant = 'primary',
  ...props
}: PressableProps & { buttonVariant?: keyof typeof buttonVariants }) => {
  const { className, children, ...otherProps } = props;

  return (
    <Pressable
      className={cn(
        'flex h-12 w-full items-center justify-center rounded-xl',
        buttonVariant && buttonVariants[buttonVariant],
        className
      )}
      {...otherProps}>
      {children}
    </Pressable>
  );
};

export default CustomButton;
