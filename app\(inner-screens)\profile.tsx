import CustomButton from '@/components/ui/button';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { goalOptions } from '@/constants/onboarding';
import { SecureStorage } from '@/lib/utils';
import { handleGoogleSignOut } from '@/services/auth/google-auth';
import { useGetMe } from '@/services/auth/me';
import { Stack, router } from 'expo-router';
import React from 'react';
import { Image, View } from 'react-native';

const ProfileScreen = () => {
  const { data } = useGetMe({});
  const handleLogout = async () => {
    await handleGoogleSignOut();
    await SecureStorage.clearAll();
    router.replace('/auth');
  };

  const userInitial = data?.first_name?.[0]?.toUpperCase() ?? '?';

  console.log(data, 'data');

  return (
    <SafeAreaContainer className="flex-1 bg-white px-6">
      <Stack.Screen options={{ headerTitle: 'Profile' }} />

      <View className="mt-8 flex-1 items-center">
        {/* Avatar */}
        <View className="mb-6 h-32 w-32 items-center justify-center rounded-full bg-secondary shadow-md">
          {data?.profile_picture ? (
            <Image
              source={{ uri: data.profile_picture }}
              className="h-full w-full rounded-full"
              resizeMode="cover"
            />
          ) : (
            <CustomText className="text-6xl text-white">{userInitial}</CustomText>
          )}
        </View>

        {/* Name and Email */}
        <CustomText className="mb-1 text-2xl font-bold text-gray-900">
          {data?.first_name} {data?.last_name}
        </CustomText>
        <CustomText className="mb-8 text-gray-500">{data?.email}</CustomText>

        {/* Info Card */}
        <View className="w-full rounded-2xl border border-gray-200 bg-gray-50 p-5 shadow-sm">
          <CustomText className="mb-3 text-lg font-semibold text-gray-800">
            Personal Info
          </CustomText>

          {data?.age && (
            <CustomText className="mb-1 text-base text-gray-700">Age: {data.age}</CustomText>
          )}
          {data?.gender && (
            <CustomText className="mb-1 text-base text-gray-700">Gender: {data.gender}</CustomText>
          )}
          {data?.height && (
            <CustomText className="mb-1 text-base text-gray-700">
              Height: {data.height} cm
            </CustomText>
          )}
          {data?.weight && (
            <CustomText className="mb-1 text-base text-gray-700">
              Weight: {data.weight} kg
            </CustomText>
          )}
          {data?.bmi && (
            <CustomText className="mb-1 text-base text-gray-700">BMI: {data.bmi}</CustomText>
          )}
          {data?.dietary_preference && (
            <CustomText className="mb-1 text-base text-gray-700">
              Dietary Preference: {data.dietary_preference}
            </CustomText>
          )}
          {data?.food_intolerances && (
            <CustomText className="mb-1 text-base text-gray-700">
              Food Intolerances: {data.food_intolerances}
            </CustomText>
          )}
          {data?.goal && (
            <CustomText className="text-base text-gray-700">
              Goal: {goalOptions.find((option) => option.value === data.goal)?.label}
            </CustomText>
          )}
        </View>

        {/* Logout Button */}
        <View className="mb-10 mt-auto w-full">
          <CustomButton
            buttonVariant="destructive"
            onPress={handleLogout}
            className="w-full items-center rounded-xl py-3 shadow-md ">
            <CustomText className="text-lg font-semibold text-white">Logout</CustomText>
          </CustomButton>
        </View>
      </View>
    </SafeAreaContainer>
  );
};

export default ProfileScreen;
