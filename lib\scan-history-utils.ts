import { Macro } from '@/components/scan-and-check/types';
import { MealCardProps } from '@/components/track-meals/types';
import { Macros, ScanHistoryItem } from '@/services/scan/types';

const macroName = (key: string) => {
  // Remove '_g' suffix
  let name = key.replace(/_g$/, '');
  // Replace underscores with spaces
  name = name.replace(/_/g, ' ');
  // Capitalize each word
  name = name.replace(/\b\w/g, (char) => char.toUpperCase());
  // Replace "Fat" with "Fats"
  name = name.replace(/\bFat\b/g, 'Fats');
  return name;
};

// Utility function to determine meal type based on timestamp
export const getMealType = (timestamp: number): string => {
  const date = new Date(timestamp);
  const hour = date.getHours();

  if (hour >= 4 && hour < 12) {
    return 'Breakfast';
  } else if (hour >= 12 && hour < 15) {
    return 'Lunch';
  } else if (hour >= 19 && hour < 24) {
    return 'Dinner';
  } else {
    return 'Snack';
  }
};

export const transformScanHistoryToMealCard = (item: ScanHistoryItem): MealCardProps => {
  // Parse macros from string to object if needed
  let macrosData: Macros;
  try {
    macrosData =
      typeof item.nutrition_label.macros === 'string'
        ? JSON.parse(item.nutrition_label.macros)
        : item.nutrition_label.macros;
  } catch (error) {
    console.error('Failed to parse macros:', error);
    macrosData = {} as Macros;
  }

  // Convert macros object to array format expected by MealCard
  const macrosArray: Macro[] = Object.entries(macrosData)
    .filter(([, value]) => value && Number(value) > 0)
    .map(([key, value], index) => ({
      id: index,
      key: key as keyof Macros,
      name: macroName(key),
      value: Number(value),
    }));

  // Use the original_base64 directly as the image URI
  const imageUri = item.original_base64;

  // Format calories
  const calories = `${item.nutrition_label.calories} Cal`;

  return {
    uri: imageUri,
    mealName: item.nutrition_label.food_name,
    calories,
    macros: macrosArray,
    timestamp: new Date(item.created_at).getTime(),
  };
};
