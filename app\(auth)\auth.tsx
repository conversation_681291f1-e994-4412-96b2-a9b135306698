import AndroidGoogleSignIn from '@/components/android-google-login';
import Form from '@/components/ui/form';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { Image, View } from 'react-native';

const AuthScreen = () => {
  //   const [authType, setAuthType] = useState<'login' | 'signup'>('login');
  return (
    <SafeAreaContainer className="scrollbar-hide flex-1 bg-background">
      <Form>
        <View className="flex flex-1 items-center justify-start bg-background">
          <Image
            source={{
              uri: 'https://lh3.googleusercontent.com/aida-public/AB6AXuANLNwikKCSldq767P4JXTatZwYehqqCqnkcdX_DWeYOqmdX7hYHzmcT_6BxivdH7dcfml12rwDBwxjnV9q1YDIZ15Qzzi4VeNyF5yWArUMWWWy0_k1UUK3E60QJzt9ZC1Rc_p7W-yxD1if-1GhNSRsImkcjDTjAjO2VzuVvNzcHjYb_q7UiYRQKuI_66vy6ZcZAD9_LA5KQpI6qO1KIdTaZ-t3FQJxDoc5L-dT_1ZCgLaRFdPXR6ykWjb791lGtvW5rSnOaubJEe4',
            }}
            className="h-[25vh] w-[50vw] rounded-xl"
            resizeMode="contain"
          />

          <View className="py-10">
            <CustomText className="ios:text-3xl text-center font-lexend-semibold text-2xl">
              Scan. Understand. Eat healthier with{''}
            </CustomText>
            <CustomText className="ios:pt-5 pt-3 text-center font-poppins-black text-5xl text-primary">
              &quot;Eatable?&quot;
            </CustomText>
          </View>
          {/* {authType === 'login' ? (
            <LoginForm OnAuthTypeChange={setAuthType} />
          ) : (
            <SignupForm OnAuthTypeChange={setAuthType} />
          )} */}
          <AndroidGoogleSignIn />
        </View>
      </Form>
    </SafeAreaContainer>
  );
};

export default AuthScreen;
