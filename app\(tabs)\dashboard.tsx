import MacroIndicators from '@/components/dashboard/macro-indicators';
import PetCard from '@/components/dashboard/pet-card';
import RecentlyEaten from '@/components/dashboard/recently-eaten';
import SafeAreaContainer from '@/components/ui/safe-area-container';
import { CustomText } from '@/components/ui/text';
import { useGetMe } from '@/services/auth/me';
import { useScanHistory } from '@/services/scan/scan';
import { Link } from 'expo-router';
import { ScrollView, View, RefreshControl } from 'react-native';
import AnimatedProgressWheel from 'react-native-progress-wheel';
import { useCallback } from 'react';

const DashboardScreen = () => {
  const { data, refetch: refetchMe, isRefetching: isRefetchingMe } = useGetMe({});
  const { refetch: refetchScanHistory, isRefetching: isRefetchingScanHistory } = useScanHistory();
  const today = new Date();
  const formattedDate = today.toLocaleDateString('en-US', { day: 'numeric', month: 'long' });

  // Handle refresh - refetch both user data and scan history
  const handleRefresh = useCallback(async () => {
    await Promise.all([refetchMe(), refetchScanHistory()]);
  }, [refetchMe, refetchScanHistory]);

  const isRefreshing = isRefetchingMe || isRefetchingScanHistory;

  return (
    <SafeAreaContainer className=" pb-0">
      {/* TODO: temp logout button */}
      <ScrollView
        showsVerticalScrollIndicator={false}
        className="flex-1"
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={['#006d77']}
            tintColor="#006d77"
          />
        }>
        <View className="flex-row justify-between">
          <View>
            <CustomText className="font-poppins text-foreground" variant="h1">
              Hello, {data?.first_name}
            </CustomText>
            <CustomText className="font-lexend text-primary">Today, {formattedDate}</CustomText>
          </View>

          <Link
            href="/profile"
            className="size-12 rounded-full bg-accent pt-2 text-center text-2xl text-white">
            {data?.first_name[0]}
          </Link>
        </View>

        <View className="mb-5 mt-10 items-center justify-center">
          <AnimatedProgressWheel
            size={260}
            width={30}
            color="#006d77"
            progress={1200}
            max={data?.daily_calories || 0}
            rounded
            rotation="-90deg"
            showProgressLabel
            labelStyle={{
              fontFamily: 'LexendDeca_700Bold',
              fontSize: 24,
            }}
            subtitle={`/ ${data?.daily_calories || 0} Cal`}
            subtitleStyle={{
              fontSize: 16,
              maxWidth: 200,
              fontFamily: 'LexendDeca_400Regular',
              textAlign: 'center',
            }}
            backgroundColor="#83c5be"
          />
        </View>

        <MacroIndicators
          data={{
            daily_carbs_g: Math.round(Number(data?.daily_carbs_g || '0')).toString(),
            daily_fat_g: Math.round(Number(data?.daily_fat_g || '0')).toString(),
            daily_protein_g: Math.round(Number(data?.daily_protein_g || '0')).toString(),
          }}
        />

        <PetCard />

        <RecentlyEaten />
      </ScrollView>
    </SafeAreaContainer>
  );
};

export default DashboardScreen;
