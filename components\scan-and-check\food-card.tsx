import { CustomText } from '@/components/ui/text';
import { Image, ScrollView, View } from 'react-native';
import { FoodCardProps } from './types';
import { MacroCard } from './macro-card';

export const FoodCard = ({ image, foodName, calories, macros }: FoodCardProps) => {
  return (
    <View className="overflow-hidden rounded-2xl bg-card shadow-sm">
      <Image source={{ uri: image }} className="h-40 w-full" resizeMode="cover" />
      <View className="w-full p-4">
        <CustomText variant="h3" className="mb-1 font-poppins text-primary">
          {foodName}
        </CustomText>
        <CustomText className="mb-2 font-poppins text-xl">
          {calories}
          <CustomText className="font-poppins text-muted-foreground"> Calories</CustomText>
        </CustomText>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="w-auto flex-row items-start justify-start gap-3">
            {macros.map((macro) => (
              <MacroCard key={macro.key} macro={macro} />
            ))}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};
